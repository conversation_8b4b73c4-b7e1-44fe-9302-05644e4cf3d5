import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { VscArrowLeft, VscCloudUpload } from 'react-icons/vsc';
import emailjs from '@emailjs/browser';

interface FormData {
  name: string;
  email: string;
  phone: string;
  platform: string;
  carChoice: string;
  bodyKit: string;
  wheels: string;
  designStyle: string;
  colors: string;
  additionalRequests: string;
  referenceImages: File[];
}

const RequestLiveryPage: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    platform: '',
    carChoice: '',
    bodyKit: '',
    wheels: '',
    designStyle: '',
    colors: '',
    additionalRequests: '',
    referenceImages: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [dragActive, setDragActive] = useState(false);

  // Initialize EmailJS
  useEffect(() => {
    emailjs.init("OYVQiZ2RMjNZl9_iK"); //Public Key from EmailJS dashboard
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({
        ...prev,
        referenceImages: [...prev.referenceImages, ...fileArray].slice(0, 5) // Max 5 files
      }));
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files);
    }
  };

  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      referenceImages: prev.referenceImages.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Prepare email template parameters
      const templateParams = {
        to_email: '<EMAIL>', // Client's email
        from_name: formData.name,
        from_email: formData.email,
        phone: formData.phone || 'Not provided',
        platform: formData.platform,
        price: getPrice(),
        car_choice: formData.carChoice,
        body_kit: formData.bodyKit || 'Not specified',
        wheels: formData.wheels || 'Not specified',
        design_style: formData.designStyle,
        colors: formData.colors,
        additional_requests: formData.additionalRequests || 'None',
        has_images: formData.referenceImages.length > 0 ? 'Yes' : 'No',
        image_count: formData.referenceImages.length,
        image_note: formData.referenceImages.length > 0 
          ? `Customer has ${formData.referenceImages.length} reference image(s). Please contact them directly for images.`
          : 'No reference images provided.',
        submission_date: new Date().toLocaleString(),
      };

      // Send email using EmailJS
      const result = await emailjs.send(
        'service_tbnov0u', // Your service ID
        'template_livery_request', // You'll need to create this template in EmailJS
        templateParams
      );

      if (result.status === 200) {
        setSubmitStatus('success');
        setFormData({
          name: '',
          email: '',
          phone: '',
          platform: '',
          carChoice: '',
          bodyKit: '',
          wheels: '',
          designStyle: '',
          colors: '',
          additionalRequests: '',
          referenceImages: []
        });
      } else {
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('EmailJS Error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getPrice = () => {
    if (formData.platform === 'console') return '$20 USD';
    if (formData.platform === 'pc') return '$35 USD';
    return '';
  };

  return (
    <>
      <Head>
        <title>Request Custom Livery - Tezz Designs</title>
        <meta name="description" content="Request a custom CarX livery design from Tezz Designs. Professional automotive livery design services." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <main className="bg-black text-white min-h-screen">
        {/* Header */}
        <header className="bg-gradient-to-r from-gray-900 to-black border-b border-gray-800">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex items-center justify-between">
              <Link 
                href="/"
                className="flex items-center text-gray-300 hover:text-yellow-400 transition-colors duration-300"
                aria-label="Go back to home page"
              >
                <VscArrowLeft className="w-5 h-5 mr-2" />
                Back to Home
              </Link>
              
              <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Tezz Designs
              </h1>
            </div>
          </div>
        </header>

        {/* Form Section */}
        <section className="py-12 px-6">
          <div className="max-w-4xl mx-auto">
            {/* Page Title */}
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Livery Request Form
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto mb-6"></div>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto mb-8">
                Please fill this form so I can ensure I don't make any mistakes with your livery
              </p>
              
              {/* Pricing Display */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-lg border border-gray-700 p-6">
                  <h3 className="text-xl font-bold text-yellow-400 mb-2">Console Livery</h3>
                  <p className="text-3xl font-bold text-white">$20 USD</p>
                </div>
                <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-lg border border-gray-700 p-6">
                  <h3 className="text-xl font-bold text-yellow-400 mb-2">PC Livery</h3>
                  <p className="text-3xl font-bold text-white">$35 USD</p>
                  <p className="text-sm text-gray-400 mt-1">More layers</p>
                </div>
              </div>
            </div>

            {/* Success Message */}
            {submitStatus === 'success' && (
              <div className="mb-8 p-6 bg-green-900/20 border border-green-500/50 rounded-lg text-center">
                <h3 className="text-xl font-bold text-green-400 mb-2">Request Submitted Successfully!</h3>
                <p className="text-green-300">Your name will be added to the livery list once payment is received. ✨🤙</p>
              </div>
            )}

            {/* Error Message */}
            {submitStatus === 'error' && (
              <div className="mb-8 p-6 bg-red-900/20 border border-red-500/50 rounded-lg text-center">
                <h3 className="text-xl font-bold text-red-400 mb-2">Submission Failed</h3>
                <p className="text-red-300">Please try again or contact us directly.</p>
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="bg-gradient-to-br from-gray-900/50 to-gray-800/50 rounded-2xl border border-gray-700 p-8">
                {/* Contact Information */}
                <div className="mb-8">
                  <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                    <span className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-black font-bold text-sm mr-3">1</span>
                    Contact Information
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                        Your Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="Your full name"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                        Phone Number (Optional)
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="+****************"
                      />
                    </div>
                  </div>
                </div>

                {/* Platform & Pricing */}
                <div className="mb-8">
                  <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                    <span className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-black font-bold text-sm mr-3">2</span>
                    Platform & Pricing
                  </h3>
                  
                  <div>
                    <label htmlFor="platform" className="block text-sm font-medium text-gray-300 mb-2">
                      Choose Platform *
                    </label>
                    <select
                      id="platform"
                      name="platform"
                      value={formData.platform}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                    >
                      <option value="">Select your platform</option>
                      <option value="console">Console - $20 USD</option>
                      <option value="pc">PC - $35 USD (More layers)</option>
                    </select>
                    {getPrice() && (
                      <p className="mt-2 text-yellow-400 font-bold">Selected Price: {getPrice()}</p>
                    )}
                  </div>
                </div>

                {/* Vehicle Specifications */}
                <div className="mb-8">
                  <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                    <span className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-black font-bold text-sm mr-3">3</span>
                    Vehicle Specifications
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="carChoice" className="block text-sm font-medium text-gray-300 mb-2">
                        Car of Choice *
                      </label>
                      <input
                        type="text"
                        id="carChoice"
                        name="carChoice"
                        value={formData.carChoice}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="e.g., BMW E46"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="bodyKit" className="block text-sm font-medium text-gray-300 mb-2">
                        Body Kit
                      </label>
                      <input
                        type="text"
                        id="bodyKit"
                        name="bodyKit"
                        value={formData.bodyKit}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="e.g., R&T"
                      />
                    </div>
                    
                    <div className="md:col-span-2">
                      <label htmlFor="wheels" className="block text-sm font-medium text-gray-300 mb-2">
                        Wheels (Optional - Don't matter for design)
                      </label>
                      <input
                        type="text"
                        id="wheels"
                        name="wheels"
                        value={formData.wheels}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="Any specific wheel preference (optional)"
                      />
                    </div>
                  </div>
                </div>

                {/* Design Details */}
                <div className="mb-8">
                  <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                    <span className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-black font-bold text-sm mr-3">4</span>
                    Design Details
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="designStyle" className="block text-sm font-medium text-gray-300 mb-2">
                        Design Style *
                      </label>
                      <input
                        type="text"
                        id="designStyle"
                        name="designStyle"
                        value={formData.designStyle}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="e.g., Comp style, drift style, racing, etc."
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="colors" className="block text-sm font-medium text-gray-300 mb-2">
                        Colors *
                      </label>
                      <input
                        type="text"
                        id="colors"
                        name="colors"
                        value={formData.colors}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="e.g., Green, blue and white, etc."
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="additionalRequests" className="block text-sm font-medium text-gray-300 mb-2">
                      Additional Requests
                    </label>
                    <textarea
                      id="additionalRequests"
                      name="additionalRequests"
                      value={formData.additionalRequests}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300 resize-none"
                      placeholder="Anything else you want to add or take away from your design? Any specific logos, sponsors, or modifications?"
                    />
                  </div>
                </div>

                {/* Reference Images */}
                <div className="mb-8">
                  <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                    <span className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-black font-bold text-sm mr-3">5</span>
                    Reference Images
                  </h3>
                  
                  <div 
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 ${
                      dragActive 
                        ? 'border-yellow-400 bg-yellow-400/10' 
                        : 'border-gray-600 hover:border-gray-500'
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <VscCloudUpload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-300 mb-2">
                      Upload reference photos/designs (optional)
                    </p>
                    <label className="inline-block cursor-pointer">
                      <span className="bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-black font-bold px-6 py-2 rounded-lg transition-all duration-300">
                        Browse Files
                      </span>
                      <input
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={(e) => handleFileUpload(e.target.files)}
                        className="hidden"
                      />
                    </label>
                    <p className="text-sm text-gray-500 mt-2">
                      Max 5 files, up to 10MB each (PNG, JPG, GIF)
                    </p>
                    <div className="mt-3 p-3 bg-blue-900/20 border border-blue-500/50 rounded text-sm">
                      <p className="text-blue-300">
                        📧 <strong>Note:</strong> Reference images will be noted in your request. 
                        If you select images, Tezz will contact you directly to receive them via email.
                      </p>
                    </div>
                  </div>
                  
                  {formData.referenceImages.length > 0 && (
                    <div className="mt-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                      {formData.referenceImages.map((file, index) => (
                        <div key={index} className="relative group">
                          <div className="bg-gray-800 rounded-lg p-3 border border-gray-600">
                            <div className="text-xs text-gray-300 truncate">{file.name}</div>
                            <div className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</div>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFile(index)}
                            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs flex items-center justify-center transition-colors duration-300"
                            aria-label={`Remove ${file.name}`}
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Important Note */}
                <div className="mb-8 p-6 bg-red-900/20 border border-red-500/50 rounded-lg">
                  <h4 className="text-lg font-bold text-red-400 mb-3">⚠️ Important Copyright Notice</h4>
                  <p className="text-red-300 text-sm leading-relaxed">
                    I cannot do 100% copies or dupes of a livery that you send me as a form of a design unless you or I can get permission from the original owner to do so!
                  </p>
                </div>

                {/* Payment Information */}
                <div className="mb-8 p-6 bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border border-yellow-500/50 rounded-lg">
                  <h4 className="text-xl font-bold text-yellow-400 mb-4">💳 Payment Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                    <div>
                      <h5 className="font-bold text-white mb-2">New Zealand Bank Transfer:</h5>
                      <p className="text-gray-300">Account: 02-0404-0278295-083</p>
                      <p className="text-gray-400">(NZ customers only)</p>
                    </div>
                    <div>
                      <h5 className="font-bold text-white mb-2">PayPal:</h5>
                      <p className="text-gray-300"><EMAIL></p>
                      <p className="text-gray-300">OR</p>
                      <p className="text-gray-300"><EMAIL></p>
                    </div>
                  </div>
                  <div className="mt-4 p-3 bg-green-900/30 border border-green-500/50 rounded text-center">
                    <p className="text-green-300 font-bold">
                      ✨ Once payment is received, your name goes onto the livery list! ✨🤙
                    </p>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="text-center">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`inline-flex items-center px-8 py-4 text-lg font-bold rounded-lg transition-all duration-300 ${
                      isSubmitting
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-black transform hover:scale-105 shadow-lg hover:shadow-xl'
                    }`}
                    aria-label="Submit livery request form"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Submitting Request...
                      </>
                    ) : (
                      <>
                        Submit Livery Request
                        <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </section>
      </main>
    </>
  );
};

export default RequestLiveryPage; 